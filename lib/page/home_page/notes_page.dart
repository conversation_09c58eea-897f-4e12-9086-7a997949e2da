import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/dao/reading_time.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/page/book_notes_page.dart';
import 'package:dasso_reader/utils/date/convert_seconds.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/highlight_digit.dart';
import 'package:dasso_reader/widgets/tips/notes_tips.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dasso_reader/config/design_system.dart';

class NotesPage extends StatefulWidget {
  const NotesPage({super.key});

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> {
  @override
  void initState() {
    super.initState();
    initialBook();
  }

  void initialBook() async {
    List<Map<String, int>> bookIdAndNotes =
        await selectAllBookIdAndNotesExcludingBookmarks();

    if (bookIdAndNotes.isNotEmpty && mounted) {
      Book book = await selectBookById(bookIdAndNotes[0]['bookId']!);
      if (mounted) {
        Provider.of<NotesDetailModel>(context, listen: false)
            .updateCurrentBook(book, bookIdAndNotes[0]['numberOfNotes']!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 600) {
              return Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        notesStatistic(),
                        bookNotesList(false),
                      ],
                    ),
                  ),
                  const VerticalDivider(thickness: 1, width: 1),
                  const Expanded(
                    flex: 2,
                    child: NotesDetail(),
                  ),
                ],
              );
            } else {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  notesStatistic(),
                  bookNotesList(true),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget notesStatistic() {
    TextStyle digitStyle = Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeHeadingM,
              ),
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
              // fontFamily: 'SourceHanSerif',
            ) ??
        TextStyle(
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeHeadingM,
          ),
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        );
    TextStyle textStyle = Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeXL,
              ),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
              // fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansSC',
            ) ??
        TextStyle(
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeXL,
          ),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
          fontFamily: 'NotoSansSC',
        );
    return FutureBuilder<Map<String, int>>(
      future: selectNumberOfNotesAndBooksExcludingBookmarks(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceS + 2),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                highlightDigit(
                  context,
                  L10n.of(context)
                      .notes_notes_across(snapshot.data!['numberOfNotes']!),
                  textStyle,
                  digitStyle,
                ),
                highlightDigit(
                  context,
                  L10n.of(context)
                      .notes_books(snapshot.data!['numberOfBooks']!),
                  textStyle,
                  digitStyle,
                ),
              ],
            ),
          );
        } else {
          return const CircularProgressIndicator();
        }
      },
    );
  }

  Widget bookNotesList(bool isMobile) {
    return FutureBuilder<List<Map<String, int>>>(
      future: selectAllBookIdAndNotesExcludingBookmarks(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return snapshot.data!.isEmpty
              ? const Expanded(child: Center(child: NotesTips()))
              : Expanded(
                  child: ListView.builder(
                    itemCount: snapshot.data!.length,
                    itemBuilder: (context, index) {
                      return bookNotes(
                        context,
                        snapshot.data![index]['bookId']!,
                        snapshot.data![index]['numberOfNotes']!,
                        isMobile,
                      );
                    },
                  ),
                );
        } else {
          return const CircularProgressIndicator();
        }
      },
    );
  }

  Widget bookNotes(
    BuildContext context,
    int bookId,
    int numberOfNotes,
    bool isMobile,
  ) {
    TextStyle digitStyle = Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeHeadingL,
              ),
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ) ??
        TextStyle(
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeHeadingL,
          ),
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        );
    TextStyle textStyle = Theme.of(context).textTheme.displaySmall?.copyWith(
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeXXL,
              ),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ) ??
        TextStyle(
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeXXL,
          ),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
        );
    TextStyle titleStyle = Theme.of(context).textTheme.titleLarge?.copyWith(
              overflow: TextOverflow.ellipsis,
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeXL,
              ),
              fontFamily: 'NotoSansSC',
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ) ??
        TextStyle(
          overflow: TextOverflow.ellipsis,
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeXL,
          ),
          fontFamily: 'NotoSansSC',
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        );
    TextStyle readingTimeStyle = Theme.of(context)
            .textTheme
            .bodyMedium
            ?.copyWith(
              fontSize: DesignSystem.getAdjustedFontSize(
                context,
                DesignSystem.fontSizeM,
              ),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ) ??
        TextStyle(
          fontSize: DesignSystem.getAdjustedFontSize(
            context,
            DesignSystem.fontSizeM,
          ),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
        );
    return FutureBuilder<Book>(
      future: selectBookById(bookId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return GestureDetector(
            onTap: () {
              if (isMobile) {
                Navigator.push(
                  context,
                  MaterialPageRoute<void>(
                    builder: (context) => BookNotesPage(
                      book: snapshot.data!,
                      numberOfNotes: numberOfNotes,
                      isMobile: true,
                    ),
                  ),
                );
              } else {
                Provider.of<NotesDetailModel>(context, listen: false)
                    .updateCurrentBook(snapshot.data!, numberOfNotes);
              }
            },
            child: Card(
              margin: const EdgeInsets.only(
                top: DesignSystem.spaceS,
                left: DesignSystem.spaceM - 1,
                right: DesignSystem.spaceM - 1,
              ),
              child: Padding(
                padding: const EdgeInsets.all(DesignSystem.spaceS),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          highlightDigit(
                            context,
                            L10n.of(context).notes_notes(numberOfNotes),
                            textStyle,
                            digitStyle,
                          ),
                          const SizedBox(height: 8),
                          Text(snapshot.data!.title, style: titleStyle),
                          const SizedBox(height: 18),
                          // Reading time
                          FutureBuilder<int>(
                            future: selectTotalReadingTimeByBookId(bookId),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.done) {
                                return Text(
                                  convertSeconds(snapshot.data!),
                                  style: readingTimeStyle,
                                );
                              } else {
                                return Text(
                                  convertSeconds(0),
                                  style: readingTimeStyle,
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                    // Expanded(child: SizedBox()),
                    Hero(
                      tag: isMobile
                          ? snapshot.data!.coverFullPath
                          : '${snapshot.data!.coverFullPath}notMobile',
                      child: bookCover(
                        context,
                        snapshot.data!,
                        height: 130,
                        width: 90,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          return const CircularProgressIndicator();
        }
      },
    );
  }
}

class NotesDetail extends StatelessWidget {
  const NotesDetail({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NotesDetailModel>(
      builder: (context, model, child) {
        return model.currentBookNotes;
      },
    );
  }
}

class NotesDetailModel with ChangeNotifier {
  Book? currentBook;
  int currentNumberOfNotes = 0;

  Widget get currentBookNotes {
    return currentBook == null
        ? const Center(child: NotesTips())
        : BookNotesPage(
            isMobile: false,
            book: currentBook!,
            numberOfNotes: currentNumberOfNotes,
          );
  }

  void updateCurrentBook(Book book, int numberOfNotes) {
    currentBook = book;
    currentNumberOfNotes = numberOfNotes;
    notifyListeners();
  }
}
